import pygame
import math
import random
import time

# 初始化 Pygame
pygame.init()

# 设置窗口
WIDTH = 1200
HEIGHT = 800
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Cyberpunk Heart - Tech Edition")

# 颜色定义
BLACK = (0, 0, 0)
NEON_BLUE = (0, 255, 255)
NEON_PINK = (255, 20, 147)
NEON_GREEN = (57, 255, 20)
NEON_PURPLE = (255, 0, 255)
NEON_CYAN = (0, 255, 255)
GRID_COLOR = (20, 40, 80)
COLORS = [NEON_BLUE, NEON_PINK, NEON_GREEN, NEON_PURPLE, NEON_CYAN]

# 科技特效类
class TechEffect:
    def __init__(self):
        self.scan_line_pos = 0
        self.grid_size = 30
        self.matrix_chars = "0123456789ABCDEF"
        self.matrix_drops = [(random.randint(0, WIDTH), random.randint(-HEIGHT, 0)) 
                           for _ in range(50)]
        self.pulse_radius = 0
        self.dna_offset = 0
        self.data_streams = [(random.randint(0, WIDTH), -100, random.uniform(2, 5)) 
                            for _ in range(10)]
        
    def draw_grid(self, surface):
        for x in range(0, WIDTH, self.grid_size):
            alpha = abs(math.sin(time.time() + x * 0.01)) * 100 + 50
            color = (*GRID_COLOR, int(alpha))
            surf = pygame.Surface((2, HEIGHT), pygame.SRCALPHA)
            pygame.draw.line(surf, color, (0, 0), (0, HEIGHT))
            surface.blit(surf, (x, 0))
        
        for y in range(0, HEIGHT, self.grid_size):
            alpha = abs(math.sin(time.time() + y * 0.01)) * 100 + 50
            color = (*GRID_COLOR, int(alpha))
            surf = pygame.Surface((WIDTH, 2), pygame.SRCALPHA)
            pygame.draw.line(surf, color, (0, 0), (WIDTH, 0))
            surface.blit(surf, (0, y))
    
    def draw_data_stream(self, surface):
        font = pygame.font.SysFont('consolas', 12)
        for i, (x, y, speed) in enumerate(self.data_streams):
            stream_text = "".join(random.choice("01") for _ in range(10))
            for j, char in enumerate(stream_text):
                alpha = max(0, min(255, 255 - abs(y + j * 15 - HEIGHT/2)))
                color = (*NEON_GREEN[:3], int(alpha))
                text = font.render(char, True, color)
                surface.blit(text, (x, y + j * 15))
                
            y += speed
            if y > HEIGHT + 100:
                y = -100
                x = random.randint(0, WIDTH)
            self.data_streams[i] = (x, y, speed)
    
    def draw_scan_line(self, surface):
        self.scan_line_pos = (self.scan_line_pos + 2) % HEIGHT
        scan_line = pygame.Surface((WIDTH, 4), pygame.SRCALPHA)
        for i in range(4):
            alpha = 255 - i * 60
            pygame.draw.line(scan_line, (*NEON_CYAN, alpha), (0, i), (WIDTH, i))
        surface.blit(scan_line, (0, self.scan_line_pos))
        
    def draw_matrix_rain(self, surface):
        font = pygame.font.SysFont('consolas', 14)
        for i, (x, y) in enumerate(self.matrix_drops):
            char = random.choice(self.matrix_chars)
            alpha = max(0, min(255, 255 - y * 0.5))
            color = (*NEON_GREEN[:3], int(alpha))
            text = font.render(char, True, color)
            surface.blit(text, (x, y))
            self.matrix_drops[i] = (x, (y + 5) % (HEIGHT + 100))
            if y > HEIGHT and random.random() < 0.1:
                self.matrix_drops[i] = (random.randint(0, WIDTH), -10)
                
    def draw_dna_helix(self, surface):
        self.dna_offset += 0.05
        prev_y1 = prev_y2 = HEIGHT//2
        for i in range(0, WIDTH + 40, 40):
            x = i - 20
            y1 = HEIGHT//2 + math.sin(self.dna_offset + i * 0.05) * 50
            y2 = HEIGHT//2 + math.sin(self.dna_offset + i * 0.05 + math.pi) * 50
            
            if i > 0:
                color1 = NEON_PINK if i % 80 == 0 else NEON_BLUE
                color2 = NEON_BLUE if i % 80 == 0 else NEON_PINK
                pygame.draw.line(surface, color1, (x-40, prev_y1), (x, y1), 2)
                pygame.draw.line(surface, color2, (x-40, prev_y2), (x, y2), 2)
                pygame.draw.circle(surface, NEON_PURPLE, (x, int(y1)), 3)
                pygame.draw.circle(surface, NEON_PURPLE, (x, int(y2)), 3)
            
            prev_y1, prev_y2 = y1, y2
            
    def draw_circuit_lines(self, surface, heart_points):
        if not hasattr(self, 'circuit_points'):
            self.circuit_points = []
            for _ in range(5):
                start = random.choice(heart_points)
                angle = random.uniform(0, 2 * math.pi)
                length = random.randint(50, 150)
                end_x = start[0] + math.cos(angle) * length
                end_y = start[1] + math.sin(angle) * length
                self.circuit_points.append((start, (end_x, end_y), random.choice(COLORS)))
        
        for start, end, color in self.circuit_points:
            pygame.draw.line(surface, color, start, end, 2)
            # 添加节点
            pygame.draw.circle(surface, color, (int(start[0]), int(start[1])), 4)
            pygame.draw.circle(surface, color, (int(end[0]), int(end[1])), 4)

# 粒子类
class Particle:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.size = random.randint(2, 4)
        self.color = random.choice(COLORS)
        self.speed = random.uniform(0.5, 2)
        self.life = 255
        self.trail = [(x, y) for _ in range(5)]
        self.angle = random.uniform(0, 2 * math.pi)
        self.velocity = random.uniform(1, 3)
        
    def update(self):
        self.life -= 2
        # 更新位置，添加螺旋运动
        self.angle += 0.1
        dx = math.cos(self.angle) * self.velocity
        dy = math.sin(self.angle) * self.velocity + self.speed
        self.x += dx
        self.y += dy
        
        # 更新轨迹
        self.trail.pop(0)
        self.trail.append((self.x, self.y))
        
        return (self.life > 0 and 
                0 <= self.x <= WIDTH and 
                0 <= self.y <= HEIGHT)

    def draw(self, surface):
        # 绘制粒子轨迹
        for i in range(len(self.trail) - 1):
            alpha = int(self.life * (i / len(self.trail)))
            color = (*self.color[:3], alpha)
            start = self.trail[i]
            end = self.trail[i + 1]
            if (0 <= start[0] <= WIDTH and 
                0 <= start[1] <= HEIGHT and 
                0 <= end[0] <= WIDTH and 
                0 <= end[1] <= HEIGHT):
                pygame.draw.line(surface, color, start, end, 2)
        
        # 绘制粒子本身
        alpha = max(0, min(255, self.life))
        color = (*self.color[:3], alpha)
        surf = pygame.Surface((self.size, self.size), pygame.SRCALPHA)
        pygame.draw.circle(surf, color, (self.size//2, self.size//2), self.size//2)
        surface.blit(surf, (self.x, self.y))
        
        # 添加光晕效果
        glow_size = self.size * 2
        glow_surf = pygame.Surface((glow_size, glow_size), pygame.SRCALPHA)
        glow_color = (*self.color[:3], alpha // 4)
        pygame.draw.circle(glow_surf, glow_color, (glow_size//2, glow_size//2), glow_size//2)
        surface.blit(glow_surf, (self.x - glow_size//4, self.y - glow_size//4))

def main():
    clock = pygame.time.Clock()
    particles = []
    animation_time = 0
    running = True
    tech_effect = TechEffect()

    # 创建带Alpha通道的surface
    particle_surface = pygame.Surface((WIDTH, HEIGHT), pygame.SRCALPHA)
    overlay_surface = pygame.Surface((WIDTH, HEIGHT), pygame.SRCALPHA)
    heart_points = []
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False

        # 清屏和更新surface
        screen.fill(BLACK)
        particle_surface.fill((0, 0, 0, 0))
        overlay_surface.fill((0, 0, 0, 0))

        # 绘制背景效果
        tech_effect.draw_grid(screen)
        tech_effect.draw_matrix_rain(overlay_surface)
        tech_effect.draw_dna_helix(overlay_surface)
        tech_effect.draw_data_stream(overlay_surface)

        # 绘制心形
        animation_time += 0.05
        scale = 15
        heart_points.clear()
        for angle in range(0, 360, 5):
            rad = math.radians(angle)
            # 参数方程形式的心形曲线
            x = 16 * (math.sin(rad) ** 3)
            y = 13 * math.cos(rad) - 5 * math.cos(2*rad) - 2 * math.cos(3*rad) - math.cos(4*rad)
            
            # 缩放和移动到屏幕中心
            screen_x = x * scale + WIDTH // 2
            screen_y = -y * scale + HEIGHT // 2
            heart_points.append((screen_x, screen_y))

            # 添加粒子
            if random.random() < 0.3:  # 控制粒子生成频率
                particles.append(Particle(screen_x, screen_y))

            # 绘制心形轮廓
            color = COLORS[int(animation_time * 5) % len(COLORS)]
            pygame.draw.circle(screen, color, (int(screen_x), int(screen_y)), 2)

        # 绘制电路线
        tech_effect.draw_circuit_lines(overlay_surface, heart_points)

        # 更新和绘制所有粒子
        particles = [p for p in particles if p.update()]
        for particle in particles:
            particle.draw(particle_surface)

        # 绘制各种图层
        screen.blit(particle_surface, (0, 0))
        screen.blit(overlay_surface, (0, 0))
        
        # 添加扫描线效果
        tech_effect.draw_scan_line(screen)

        # 绘制HUD风格的文本
        font = pygame.font.SysFont('consolas', 16)
        stats_text = (f"PULSE: {int(animation_time*10):03d} | "
                     f"PARTICLES: {len(particles):04d} | "
                     f"FPS: {int(clock.get_fps()):02d}")
        text_surface = font.render(stats_text, True, NEON_GREEN)
        screen.blit(text_surface, (10, HEIGHT - 30))
        
        # 更新显示
        pygame.display.flip()
        clock.tick(60)

    pygame.quit()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        pygame.quit()
