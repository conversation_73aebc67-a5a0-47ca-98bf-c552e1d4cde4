package Core

import (
	"github.com/panjf2000/gnet/v2"
	"log"
	"sync"
	"time"
)

const (
	UPort           = 10000
	TPort           = 10001
	CleanupInterval = 5 * time.Second
	InactiveTimeout = 10 * time.Second
)

type NStream struct {
	UserID  int                    `json:"UserID"`
	Type    string                 `json:"Type"`
	Payload map[string]interface{} `json:"Payload,omitempty"`
}

type UPack struct {
	Connection gnet.Conn
	LastActive time.Time
	Motion     string
}

type TPack struct {
	Connection gnet.Conn
	LastActive time.Time
}

type NClient struct {
	UDP UPack
	TCP TPack
}

var Pool sync.Map

func NewNStream() NStream {
	Stream := NStream{
		Payload: make(map[string]interface{}),
	}
	Stream.Payload["Timestamp"] = time.Now().Unix()
	return Stream
}

func Start() {
	Pool = sync.Map{}
	if err := InitDatabase(); err != nil {
		log.Printf("数据库初始化失败: %v\n", err)
		return
	}
	StartUDP(UPort)
	StartTCP(TPort)
}
