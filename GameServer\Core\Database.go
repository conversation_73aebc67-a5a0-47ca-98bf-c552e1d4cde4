package Core

import (
	// "database/sql"
	"fmt"
	// _ "github.com/go-sql-driver/mysql"
	// "log"
	"time"
	"gorm.io/driver/mysql"
  	"gorm.io/gorm"
)

// var DB *sql.DB
var db *gorm.DB

type Letter struct {
	LetterID int64 `gorm:"primaryKey;autoIncrement"`
	UserID int64
	Content string
	StyleIndex int64
	Location string
	Rotation string
	CreateTime time.Time `gorm:"autoCreateTime;type:timestamp"`
}

func InitDatabase() error {
	dsn := "root:@tcp(localhost:3306)/game_db?charset=utf8mb4&parseTime=True&loc=Local"
	var err error
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	fmt.Println(db,err)
	db.AutoMigrate(&Letter{})
	// var err error
	// DB, err = sql.Open("mysql", "root:@tcp(localhost:3306)/game_db?charset=utf8mb4&parseTime=True&loc=Local")
	// if err != nil {
	// 	return fmt.Errorf("连接数据库失败: %v", err)
	// }

	// // 测试连接
	// err = DB.Ping()
	// if err != nil {
	// 	return fmt.Errorf("数据库连接测试失败: %v", err)
	// }
	return nil
}

func SaveLetter(userID int, content string, styleIndex int, location string, rotation string) (Letter, error) {
	// _, err := DB.Exec("INSERT INTO letters (content, style_index, location, rotation) VALUES (?, ?, ?, ?)",
	// 	content, styleIndex, location, rotation)
	// if err != nil {
	// 	return fmt.Errorf("保存信件失败: %v", err)
	// }
	letter := Letter{UserID:int64(userID),Content:content,StyleIndex:int64(styleIndex),Location:location,Rotation:rotation}
	db.Create(&letter)
	return letter, nil
} 

func GetLatestLetters(limit int) ([]Letter, error) {
	if db == nil {
		return nil, fmt.Errorf("数据库未初始化")
	}
	
	var letters []Letter
	result := db.Order("letter_id DESC").Limit(limit).Find(&letters)
	if result.Error != nil {
		return nil, fmt.Errorf("查询信件失败: %v", result.Error)
	}
	return letters, nil
}

func CheckDailyLetterLimit(userID int, maxCount int) (bool, error) {
    if db == nil {
        return false, fmt.Errorf("数据库未初始化")
    }
 
    // 获取当前日期（UTC时区，格式：YYYY-MM-DD）
    todayUTC := time.Now().UTC().Format("2006-01-02")
 
    var count int64
    // 使用数据库方言特定的日期处理函数
    result := db.Model(&Letter{}).
        Where("user_id = ? AND create_time >= ?", 
             userID, 
             todayUTC+" 00:00:00").
        Where("create_time < ?", 
             tomorrowUTC()).
        Count(&count)
 
    if result.Error != nil {
        return false, fmt.Errorf("查询信件数量失败: %v", result.Error)
    }
 
    return count >= int64(maxCount), nil
}
 
func tomorrowUTC() string {
    tomorrow := time.Now().UTC().AddDate(0, 0, 1)
    return tomorrow.Format("2006-01-02") + " 00:00:00"
}