package Core

import (
	"fmt"
	"github.com/goccy/go-json"
	"github.com/panjf2000/gnet/v2"
	"log"
	"time"
)

type USocket struct {
	gnet.BuiltinEventEngine
	Engine gnet.Engine
}

func (Self *USocket) OnBoot(Engine gnet.Engine) gnet.Action {
	Self.Engine = Engine
	log.Printf("UDP服务器初始化")
	return gnet.None
}

func (Self *USocket) OnTraffic(Connect gnet.Conn) gnet.Action {
	RawData, _ := Connect.Next(-1)
	Stream := NewNStream()
	// log.Printf("UDP消息 %s\n", RawData)
	if Error := json.Unmarshal(RawData, &Stream); Error != nil {
		log.Println("Error Message : " + string(RawData))
		log.Printf("Error : %v \n", Error)
		return gnet.None
	}
	if Value, OK := Pool.Load(Stream.UserID); OK {
		Client := Value.(*NClient)
		Client.UDP.Connection = Connect
		Client.UDP.LastActive = time.Now()
	} else {
		Client := &NClient{
			UDP: UPack{
				Connection: Connect,
				LastActive: time.Now(),
				Motion:     "",
			},
		}
		Pool.Store(Stream.UserID, Client)
	}
	Self.HandleStream(Connect, Stream)
	return gnet.None
}

func (Self *USocket) Cleanup() {
	Ticker := time.NewTicker(CleanupInterval)
	defer Ticker.Stop()
	for range Ticker.C {
		Now := time.Now()
		Pool.Range(func(Key, Value interface{}) bool {
			Client := Value.(*NClient)
			if Now.Sub(Client.UDP.LastActive) > InactiveTimeout {
				Self.CleanUser(Key.(int))
				log.Printf("用户 %d UDP连接过期\n", Key.(int))
			}
			return true
		})
	}
}

func (Self *USocket) CleanUser(UserID int) {
	Pool.Delete(UserID)
}

func (Self *USocket) Send(Message []byte, Connection gnet.Conn) {
	if Connection == nil {
		return
	}
	// log.Printf("UDP发出 %s\n", Message)
	Error := Connection.AsyncWrite(Message, func(Connect gnet.Conn, Error error) error {
		if Error != nil {
			log.Printf("发送失败: %v\n", Error)
		}
		return nil
	})
	if Error != nil {
		log.Printf("发送失败: %v\n", Error)
		return
	}
}

func (Self *USocket) SendStream(Stream NStream, Connection gnet.Conn) {
	Message, _ := json.Marshal(Stream)
	Self.Send(Message, Connection)
}

func (Self *USocket) BroadcastOther(Message []byte, SelfKey interface{}) {
	Pool.Range(func(Key, Value interface{}) bool {
		if SelfKey == Key {
			return true
		}
		Connection := Value.(*NClient).UDP.Connection
		Self.Send(Message, Connection)
		return true
	})
}

func (Self *USocket) BroadcastStreamOther(Stream NStream) {
	Message, _ := json.Marshal(Stream)
	Self.BroadcastOther(Message, Stream.UserID)
}

func (Self *USocket) Broadcast(Message []byte) {
	Self.BroadcastOther(Message, nil)
}

func (Self *USocket) BroadcastStream(Stream NStream) {
	Message, _ := json.Marshal(Stream)
	Self.Broadcast(Message)
}

func StartUDP(Port int) {
	Instance := new(USocket)
	go func() {
		go Instance.Cleanup()

		Url := fmt.Sprintf("udp://0.0.0.0:%d", Port)
		Error := gnet.Run(
			Instance, Url,
			gnet.WithMulticore(true),
			gnet.WithReusePort(true),
		)
		if Error != nil {
			log.Printf("存在错误: %v\n", Error)
			return
		}
	}()
	go func() {
		Ticker := time.NewTicker(CleanupInterval)
		for {
			<-Ticker.C
			Stream := NewNStream()
			Stream.UserID = -1
			Stream.Type = "Ping"
			Instance.BroadcastStream(Stream)
		}
	}()
}

func (Self *USocket) HandleStream(Connect gnet.Conn, Stream NStream) {
	if Stream.Type == "Pong" {
		return
	}
	if Stream.Type == "OnGuestLogout" {
		Self.CleanUser(Stream.UserID)
		log.Printf("用户 %d UDP连接退出\n", Stream.UserID)
		return
	}
	if Stream.Type == "OnGuestMotion" {
		if Motion, OK := Stream.Payload["Motion"].(string); OK {
			if Client, OK := Pool.Load(Stream.UserID); OK {
				Client.(*NClient).UDP.Motion = Motion
			}
		}
	}
	Self.BroadcastStreamOther(Stream)
}
