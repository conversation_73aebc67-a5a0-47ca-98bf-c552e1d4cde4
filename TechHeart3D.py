import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from mpl_toolkits.mplot3d import Axes3D

# 设置3D图形
fig = plt.figure(figsize=(10, 8))
ax = fig.add_subplot(111, projection='3d')
ax.set_facecolor('black')
fig.patch.set_facecolor('black')

# 设置科技感样式
ax.xaxis.pane.fill = False
ax.yaxis.pane.fill = False
ax.zaxis.pane.fill = False
ax.xaxis.pane.set_edgecolor('cyan')
ax.yaxis.pane.set_edgecolor('cyan') 
ax.zaxis.pane.set_edgecolor('cyan')
ax.grid(True, color='cyan', linestyle='--', alpha=0.5)

# 精确的3D爱心参数方程
def heart_3d(u, v):
    u = np.linspace(0, 2*np.pi, 150)  # 增加网格密度
    v = np.linspace(0, np.pi, 150)
    u, v = np.meshgrid(u, v)
    
    # 经典爱心方程
    x = 16 * np.sin(u)**3
    y = 13 * np.cos(u) - 5 * np.cos(2*u) - 2 * np.cos(3*u) - np.cos(4*u)
    
    # 3D扩展
    scale = 0.8 + 0.2 * np.cos(v)  # 保持形状完整性
    x = x * scale * np.cos(v)
    y = y * scale 
    z = 15 * np.sin(v) * scale
    
    # 标准化比例
    x = x / 16 * 12
    y = y / 16 * 12
    z = z / 15 * 10
    
    return x, y, z

# 初始化图形
x, y, z = heart_3d(0, 0)

# 科技感元素
def add_tech_elements(ax, frame):
    # 动态网格
    ax.grid(True, color=(0, 0.8, 0.8, 0.3), linestyle='--', linewidth=0.5)
    
    # 数据流粒子
    for _ in range(5):
        xp = np.random.uniform(-20, 20)
        yp = np.random.uniform(-15, 15)
        zp = np.random.uniform(-15, 15)
        ax.scatter(xp, yp, zp, color=(0, 1, 1, 0.7), s=5)
    
    # 动态标题
    ax.set_title(f'TECH HEART 3D | FRAME {frame:03d}', 
                color=(0.2, 0.8, 1), fontsize=14, fontweight='bold')

# 动画更新函数
def update(frame):
    ax.cla()
    ax.set_facecolor((0.02, 0.02, 0.1))
    for axis in [ax.xaxis, ax.yaxis, ax.zaxis]:
        axis.set_pane_color((0, 0, 0, 0))
        axis._axinfo['grid']['color'] = (0, 0.8, 0.8, 0.3)
    
    # 平滑旋转爱心 (避免变形)
    x, y, z = heart_3d(0, 0)
    angle = frame/15  # 减慢旋转速度
    # 分别绕Y轴和Z轴旋转
    x_rot = x * np.cos(angle) - z * np.sin(angle)
    z_rot = x * np.sin(angle) + z * np.cos(angle)
    y_rot = y * np.cos(angle/2) - x_rot * np.sin(angle/2)
    x_rot = x_rot * np.cos(angle/2) + y * np.sin(angle/2)
    
    # 绘制爱心，添加发光边缘
    color_val = 0.5 + 0.5 * np.sin(frame/10)
    heart_color = (color_val, 0.2, 1-color_val, 0.9)
    edge_color = (0, 1, 1, 0.3)
    
    surf = ax.plot_surface(x_rot, y, z_rot, 
                         color=heart_color,
                         edgecolor=edge_color,
                         linewidth=0.5,
                         rstride=1, cstride=1,
                         antialiased=True,
                         shade=True)
    
    # 添加科技元素
    add_tech_elements(ax, frame)
    
    # 添加科技感元素
    ax.set_title('Tech Heart 3D', color='cyan', fontsize=16)
    ax.set_xticks([])
    ax.set_yticks([])
    ax.set_zticks([])
    
    return fig,

# 创建动画
ani = FuncAnimation(fig, update, frames=100, interval=50, blit=False)

# 显示图形
plt.tight_layout()
plt.show()
